using System;
using Klee.Domain.Services.PdfGeneration.Models;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using System.IO;

namespace Klee.Domain.Services.PdfGeneration.Documents;

/// <summary>
/// QuestPDF document template for invoice generation
/// </summary>
public class InvoicePdfDocument : IDocument
{
    public InvoicePdfModel Model { get; }

    public InvoicePdfDocument(InvoicePdfModel model)
    {
        Model = model;
    }

    public DocumentMetadata GetMetadata() => new DocumentMetadata
    {
        Title = $"Invoice #{Model.InvoiceNumber}",
        Author = "Seafar Resource Planner",
        Subject = "Voyage Invoice",
        Keywords = "invoice, voyage, seafar"
    };

    public DocumentSettings GetSettings() => DocumentSettings.Default;

    public void Compose(IDocumentContainer container)
    {
        container
            .Page(page =>
            {
                page.Margin(50);
                page.Size(PageSizes.A4);
                
                page.Header().Element(ComposeHeader);
                page.Content().Element(ComposeContent);
                page.Footer().Element(ComposeFooter);
            });
    }

    void ComposeHeader(IContainer container)
    {
        container.Row(row =>
        {
            row.RelativeItem().Column(column =>
            {
                column.Item()
                    .Text($"INVOICE #{Model.InvoiceNumber}")
                    .FontSize(24).SemiBold().FontColor("#0f766e");

                column.Item().PaddingTop(10).Text(text =>
                {
                    text.Span("Issue Date: ").SemiBold();
                    text.Span($"{Model.CreatedDate.ToLocalTime():MMMM dd, yyyy}");
                });

                if (Model.PaymentDate.HasValue)
                {
                    column.Item().Text(text =>
                    {
                        text.Span("Payment Date: ").SemiBold();
                        text.Span($"{Model.PaymentDate.Value.ToLocalTime():MMMM dd, yyyy}");
                    });
                }
            });

            row.ConstantItem(150).Column(column =>
            {
                // Try to load logo, fallback to text if not available
                try
                {
                    var logoPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "Logo", "logo.png");
                    if (File.Exists(logoPath))
                    {
                        column.Item()
                            .AlignRight()
                            .Height(32)
                            .Image(logoPath);
                    }
                    else
                    {
                        column.Item()
                            .AlignRight()
                            .Text("SEAFAR")
                            .FontSize(20).SemiBold().FontColor("#0f766e");
                    }
                }
                catch
                {
                    column.Item()
                        .AlignRight()
                        .Text("SEAFAR")
                        .FontSize(20).SemiBold().FontColor("#0f766e");
                }

            });
        });
    }

    void ComposeContent(IContainer container)
    {
        container.PaddingVertical(40).Column(column =>
        {
            column.Spacing(20);

            // Organizations section
            column.Item().Element(ComposeOrganizations);

            // Voyage details section
            column.Item().Element(ComposeVoyageDetails);

            // Invoice summary section
            column.Item().Element(ComposeInvoiceSummary);
        });
    }

    void ComposeOrganizations(IContainer container)
    {
        container.Row(row =>
        {
            row.RelativeItem().Element(container => ComposeBookingOrganizationInfo(container, "BOOKING ORGANIZATION", Model.BookingOrganization));
            row.ConstantItem(50);
            row.RelativeItem().Element(container => ComposeSeafarBillingInfo(container, "BILLING TO", Model.SeafarInfo));
        });
    }

    void ComposeBookingOrganizationInfo(IContainer container, string title, InvoicePdfModel.OrganizationInfo organization)
    {
        container.Column(column =>
        {
            column.Spacing(5);

            column.Item()
                .BorderBottom(2)
                .BorderColor("#0f766e")
                .PaddingBottom(5)
                .Text(title)
                .FontSize(12).SemiBold().FontColor("#0f766e");

            column.Item().Text(organization.Name).FontSize(14).SemiBold();

            if (!string.IsNullOrEmpty(organization.Code))
                column.Item().Text($"Code: {organization.Code}").FontSize(10).FontColor("#6b7280");

            if (!string.IsNullOrEmpty(organization.Address))
                column.Item().Text(organization.Address).FontSize(10);

            if (!string.IsNullOrEmpty(organization.City))
                column.Item().Text($"{organization.City}, {organization.Country}").FontSize(10);

            if (!string.IsNullOrEmpty(organization.ContactEmail))
                column.Item().Text(organization.ContactEmail).FontSize(10).FontColor("#0f766e");

            if (!string.IsNullOrEmpty(organization.ContactPhone))
                column.Item().Text(organization.ContactPhone).FontSize(10);
        });
    }

    void ComposeSeafarBillingInfo(IContainer container, string title, InvoicePdfModel.SeafarCompanyInfo seafarInfo)
    {
        container.Column(column =>
        {
            column.Spacing(5);

            column.Item()
                .BorderBottom(2)
                .BorderColor("#0f766e")
                .PaddingBottom(5)
                .Text(title)
                .FontSize(12).SemiBold().FontColor("#0f766e");

            column.Item().Text(seafarInfo.Name).FontSize(14).SemiBold();
            column.Item().Text(seafarInfo.Address).FontSize(10);
            column.Item().Text($"IBAN: {seafarInfo.Iban}").FontSize(10).FontColor("#0f766e");
        });
    }

    void ComposeVoyageDetails(IContainer container)
    {
        container.Border(1).BorderColor("#e5e7eb").Padding(15).Column(column =>
        {
            column.Spacing(10);

            column.Item()
                .Text("VOYAGE DETAILS")
                .FontSize(14).SemiBold().FontColor("#0f766e");

            column.Item().Row(row =>
            {
                row.RelativeItem().Column(leftColumn =>
                {
                    leftColumn.Item().Text(text =>
                    {
                        text.Span("Vessel: ").SemiBold();
                        text.Span($"{Model.Voyage.VesselName} ({Model.Voyage.VesselType})");
                    });

                    leftColumn.Item().Text(text =>
                    {
                        text.Span("Operator: ").SemiBold();
                        text.Span(Model.Voyage.OperatorName);
                    });

                    leftColumn.Item().Text(text =>
                    {
                        text.Span("Operator Organization: ").SemiBold();
                        text.Span(Model.OperatorOrganization.Name);
                    });

                    leftColumn.Item().Text(text =>
                    {
                        text.Span("Duration: ").SemiBold();
                        text.Span(Model.Voyage.DurationDisplay);
                    });
                });

                row.RelativeItem().Column(rightColumn =>
                {
                    rightColumn.Item().Text(text =>
                    {
                        text.Span("Period: ").SemiBold();
                        text.Span(Model.Voyage.DateRangeDisplay);
                    });

                    rightColumn.Item().Text(text =>
                    {
                        text.Span("Status: ").SemiBold();
                        text.Span(Model.Voyage.Status.ToString());
                    });
                });
            });

            if (!string.IsNullOrEmpty(Model.Voyage.Description))
            {
                column.Item().Text(text =>
                {
                    text.Span("Description: ").SemiBold();
                    text.Span(Model.Voyage.Description);
                });
            }
        });
    }

    void ComposeInvoiceSummary(IContainer container)
    {
        container.AlignRight().Width(350).Border(1).BorderColor("#0f766e").Column(column =>
        {
            column.Item()
                .Background("#0f766e")
                .Padding(10)
                .Text("INVOICE BREAKDOWN")
                .FontSize(14).SemiBold().FontColor(Colors.White);

            column.Item().Padding(15).Column(summaryColumn =>
            {
                summaryColumn.Spacing(8);

                // Operator Cost
                summaryColumn.Item().Row(row =>
                {
                    row.RelativeItem().Text($"Operator Cost ({Model.VoyageDurationHours:F1} hours × €{(Model.OperatorCost / Model.VoyageDurationHours):F2})").FontSize(10);
                    row.ConstantItem(80).AlignRight().Text($"€{Model.OperatorCost:F2}").SemiBold();
                });

                // Roc Cost
                summaryColumn.Item().Row(row =>
                {
                    row.RelativeItem().Text($"Operator Cost ({Model.VoyageDurationHours:F1} hours × €0)").FontSize(10);
                    row.ConstantItem(80).AlignRight().Text($"€0").SemiBold();
                });

                // Commission
                summaryColumn.Item().Row(row =>
                {
                    row.RelativeItem().Text($"SEAFAR Commission ({(Model.CommissionRate * 100):F0}%)").FontSize(10);
                    row.ConstantItem(80).AlignRight().Text($"€{Math.Round(Model.CommissionAmount, 2)}").SemiBold();
                });

                // Divider
                summaryColumn.Item().BorderTop(1).BorderColor("#e5e7eb").PaddingTop(8);

                // Total
                summaryColumn.Item().Row(row =>
                {
                    row.RelativeItem().Text("Total Amount:").FontSize(12).SemiBold();
                    row.ConstantItem(80).AlignRight().Text($"€{Model.TotalAmountInEuros:F2}").FontSize(16).SemiBold().FontColor("#0f766e");
                });
            });
        });
    }

    void ComposeFooter(IContainer container)
    {
        container.Row(row =>
        {
            row.RelativeItem().Text("Generated by Seafar Resource Planner").FontSize(8).FontColor("#6b7280");
            row.ConstantItem(100).AlignRight().Text("Page 1").FontSize(8).FontColor("#6b7280");
        });
    }
}
