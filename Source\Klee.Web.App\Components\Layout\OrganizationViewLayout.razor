@using Klee.Web.App.Components.Pages.OrganizationOperatorManagement.OrganizationOperatorList
@using Klee.Web.App.Components.Pages.OrganizationVehicleManagement.OrganizationVehicleList
@using Klee.Web.App.Components.Pages.VoyagePlanning
@using Klee.Web.App.Components.Pages.InvoiceManagement.Invoices
@using Klee.Web.App.Components.Pages.VoyageManagement.VoyageList
@using Klee.Web.App.Components.Pages.VoyageManagement.FindVoyage
@inherits LayoutComponentBase

<AntContainer/>
<div class="flex flex-col bg-gray-50 h-full">
    <header class="bg-white border-b border-gray-200">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <a href="@Dashboard.GetUri()" class="flex items-center space-x-2">
                        <img src="/Logo/logo.png" alt="SEAFAR Logo" class="h-8 w-16" />
                        <span class="text-xl font-bold text-teal-700">SEAFAR</span>
                    </a>
                </div>

                <div class="flex items-center space-x-8">
                    <!-- Desktop Navigation -->
                    <nav class="hidden md:flex items-center space-x-6">
                        <a href="@VoyagePlanner.GetUri()" class="text-gray-600 hover:text-teal-700 font-medium">Find a Captain</a>
                        <a href="@FindVoyage.GetUri()" class="text-gray-600 hover:text-teal-700 font-medium">Find a Voyage</a>
                        <a href="@Invoices.GetUri()" class="text-gray-600 hover:text-teal-700 font-medium">Invoices</a>
                        <a href="@Voyages.GetUri()" class="text-gray-600 hover:text-teal-700 font-medium">My Voyages</a>
                        <button class="dropdown-toggle text-gray-600 hover:text-teal-700 font-medium" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            My Organization
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="@(Vessels.GetUri())">
                                    <i class="fas fa-ship mr-2"></i>
                                    Vessels
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="@(Operators.GetUri())">
                                    <i class="fas fa-users mr-2"></i>
                                    Operators
                                </a>
                            </li>
@*                             <li>
                                <a class="dropdown-item" href="@(Rocs.GetUri())">
                                    <i class="fas fa-building ml-1 mr-2"></i>
                                    ROCs
                                </a>
                            </li> *@
                        </ul>
                    </nav>

                    <!-- Mobile Hamburger Button -->
                    <div class="block md:hidden">
                        <button @onclick="ToggleMobileMenu"
                                data-hamburger-button
                                class="text-gray-600 hover:text-teal-700 focus:outline-none focus:text-teal-700 transition-colors duration-200"
                                aria-label="Toggle navigation menu">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation Menu -->
    <div class="@(isMobileMenuOpen ? "block mobile-nav-menu" : "hidden mobile-nav-menu") md:hidden bg-white border-b border-gray-200 shadow-sm relative z-50" data-mobile-menu>
        <nav class="container mx-auto px-4 py-4 space-y-3">
            <a href="@VoyagePlanner.GetUri()"
               class="mobile-nav-item block text-gray-600 hover:text-teal-700 font-medium py-2 px-3 rounded-md hover:bg-teal-50 transition-colors duration-200"
               @onclick="CloseMobileMenu">
                <i class="fas fa-user mr-2"></i>Find a Captain
            </a>
            <a href="@FindVoyage.GetUri()"
               class="mobile-nav-item block text-gray-600 hover:text-teal-700 font-medium py-2 px-3 rounded-md hover:bg-teal-50 transition-colors duration-200"
               @onclick="CloseMobileMenu">
                <i class="fas fa-ship mr-2"></i>Find a Voyage
            </a>
            <a href="@Invoices.GetUri()"
               class="mobile-nav-item block text-gray-600 hover:text-teal-700 font-medium py-2 px-3 rounded-md hover:bg-teal-50 transition-colors duration-200"
               @onclick="CloseMobileMenu">
                <i class="fas fa-file-invoice mr-2"></i>Invoices
            </a>
            <a href="@Voyages.GetUri()"
               class="mobile-nav-item block text-gray-600 hover:text-teal-700 font-medium py-2 px-3 rounded-md hover:bg-teal-50 transition-colors duration-200"
               @onclick="CloseMobileMenu">
                <i class="fas fa-anchor mr-2"></i>My Voyages
            </a>

            <!-- My Organization Section -->
            <div class="border-t border-gray-200 pt-3 mt-3">
                <div class="text-gray-500 text-sm font-medium px-3 mb-2">My Organization</div>
                <a href="@(Vessels.GetUri())"
                   class="mobile-nav-item block text-gray-600 hover:text-teal-700 font-medium py-2 px-3 rounded-md hover:bg-teal-50 transition-colors duration-200 ml-3"
                   @onclick="CloseMobileMenu">
                    <i class="fas fa-ship mr-2"></i>Vessels
                </a>
                <a href="@(Operators.GetUri())"
                   class="mobile-nav-item block text-gray-600 hover:text-teal-700 font-medium py-2 px-3 rounded-md hover:bg-teal-50 transition-colors duration-200 ml-3"
                   @onclick="CloseMobileMenu">
                    <i class="fas fa-users mr-2"></i>Operators
                </a>
            </div>
        </nav>
    </div>

    <main class="flex-1 overflow-auto bg-white">
        <div class="container mx-auto px-4 py-6">
            @Body
        </div>
    </main>

</div>


@code {
    private bool isMobileMenuOpen = false;

    private void ToggleMobileMenu()
    {
        isMobileMenuOpen = !isMobileMenuOpen;
        StateHasChanged();
    }

    private void CloseMobileMenu()
    {
        isMobileMenuOpen = false;
        StateHasChanged();
    }
}