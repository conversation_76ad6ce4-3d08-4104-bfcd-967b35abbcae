using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Klee.Domain.Entities.OperatorManagement.Operators;
using Klee.Domain.Entities.OrganizationManagement.Organizations;
using Klee.Domain.Entities.VehicleManagement.Vehicles;
using Klee.Domain.Entities.VoyageManagement.Voyages;
using Microsoft.Extensions.Logging;
using Monet.Helpers;


namespace Klee.Domain.Services.MessagingService;

public class VoyageNotificationService : IVoyageNotificationService
{
    private readonly IEmailService _emailService;
    private readonly ILogger<VoyageNotificationService> _logger;

    public VoyageNotificationService(
        IEmailService emailService,
        ILogger<VoyageNotificationService> logger)
    {
        _emailService = emailService;
        _logger = logger;
    }

    public async Task SendVoyageOpportunityNotificationsAsync(Voyage voyage, Vehicle vehicle, Organization bookingOrganization, List<Organization> targetOrganizations)
    {
        try
        {
            string voyageId = voyage.VoyageId.ToString();

            _logger.LogInformation("Starting voyage opportunity notifications for voyage {VoyageId}", voyageId);

            if (!targetOrganizations.Any())
            {
                _logger.LogInformation("No target organizations found for voyage opportunity notification");
                return;
            }

            // Get contact emails for each organization
            List<string> recipientEmails = targetOrganizations
                .Where(org => !string.IsNullOrWhiteSpace(org.ContactEmail))
                .Select(org => org.ContactEmail)
                .ToList();

            if (!recipientEmails.Any())
            {
                _logger.LogInformation("No recipient emails found for voyage opportunity notification");
                return;
            }

            // Generate email content
            string subject = $"New Voyage Opportunity Available - {vehicle.VehicleName}";
            string htmlContent = GenerateVoyageOpportunityEmailContent(voyage, vehicle, bookingOrganization);

            // Send emails asynchronously
         
            foreach (string email in recipientEmails)
            {
                try
                {
                    await _emailService.SendEmailAsync(email, subject, htmlContent);
                    _logger.LogDebug("Voyage opportunity notification sent to {Email} for voyage {VoyageId}", email, voyageId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to send voyage opportunity notification to {Email} for voyage {VoyageId}", email, voyageId);
                }
            }
            
            _logger.LogInformation("Completed sending voyage opportunity notifications for voyage {VoyageId} to {RecipientCount} recipients", 
                voyageId, recipientEmails.Count);
          

            _logger.LogInformation("Initiated voyage opportunity notifications for voyage {VoyageId} to {RecipientCount} recipients", 
                voyageId, recipientEmails.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending voyage opportunity notifications for voyage {VoyageId}", voyage.VoyageId);
            // Don't rethrow - we don't want email failures to affect voyage creation
        }
    }

    public Task SendVoyageBookedNotificationAsync(Voyage voyage, Vehicle vehicle, Operator op, Organization bookingOrganization) {
        return Task.CompletedTask; // Placeholder for future implementation
    }

    private string GenerateVoyageOpportunityEmailContent(Voyage voyage, Vehicle vehicle, Organization bookingOrganization)
    {
        string qualifications = voyage.RequiredQualifications.Any() 
            ? string.Join(", ", voyage.RequiredQualifications.Select(q => q.GetDisplayName()))
            : "None specified";

        return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>New Voyage Opportunity</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #0f766e; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }}
        .content {{ background-color: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }}
        .voyage-details {{ background-color: white; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #0f766e; }}
        .detail-row {{ margin: 8px 0; }}
        .label {{ font-weight: bold; color: #0f766e; }}
        .cta-button {{ background-color: #0f766e; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }}
        .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #666; }}
    </style>
</head>
<body>
    <div class='header'>
        <h1>🚢 New Voyage Opportunity Available</h1>
        <p>A new voyage is seeking a qualified captain</p>
    </div>
    
    <div class='content'>
        <p>Hello,</p>
        
        <p>A new voyage opportunity has been posted that may be suitable for your operators. Please review the details below:</p>
        
        <div class='voyage-details'>
            <h3>Voyage Details</h3>
            <div class='detail-row'><span class='label'>Booking Organization:</span> {bookingOrganization.Name}</div>
            <div class='detail-row'><span class='label'>Start Date:</span> {voyage.StartDateTime:dddd, MMMM dd, yyyy 'at' HH:mm}</div>
            <div class='detail-row'><span class='label'>End Date:</span> {voyage.EndDateTime:dddd, MMMM dd, yyyy 'at' HH:mm}</div>
            <div class='detail-row'><span class='label'>Duration:</span> {(voyage.EndDateTime - voyage.StartDateTime).TotalHours:F1} hours</div>
            {(!string.IsNullOrWhiteSpace(voyage.Description) ? $"<div class='detail-row'><span class='label'>Description:</span> {voyage.Description}</div>" : "")}
        </div>
        
        <div class='voyage-details'>
            <h3>Vessel Information</h3>
            <div class='detail-row'><span class='label'>Vessel Name:</span> {vehicle.VehicleName}</div>
            <div class='detail-row'><span class='label'>ENI:</span> {vehicle.ENI}</div>
            {(vehicle.Length > 0 ? $"<div class='detail-row'><span class='label'>Length:</span> {vehicle.Length}m</div>" : "")}
            {(vehicle.Beam > 0 ? $"<div class='detail-row'><span class='label'>Beam:</span> {vehicle.Beam}m</div>" : "")}
            {(vehicle.VesselType != Entities.VehicleManagement.Vehicles.Data.VesselTypeIds.None ? $"<div class='detail-row'><span class='label'>Vessel Type:</span> {vehicle.VesselType.GetDisplayName()}</div>" : "")}
        </div>
        
        <div class='voyage-details'>
            <h3>Required Qualifications</h3>
            <div class='detail-row'>{qualifications}</div>
        </div>
        
        <p>If you have qualified operators available for this voyage, please log in to the Resource Planner to submit your proposal.</p>
        
        <a href='#' class='cta-button'>View Voyage Details</a>
        
        <p><strong>Note:</strong> This is an automated notification. Voyage opportunities are available on a first-come, first-served basis.</p>
    </div>
    
    <div class='footer'>
        <p>Resource Planner - Seafar Maritime Solutions</p>
        <p>This email was sent automatically. Please do not reply to this email.</p>
    </div>
</body>
</html>";
    }
}
